const News = require("../../model/News.model")

module.exports.index = async (req, res) => {
    console.log("ok");
    const report = await News.find({}).sort({ createdAt: -1 });
    res.json({
        success: true,
        data: report
    })
}

module.exports.postNews = async (req, res) => {

    const { title, contentHtml, author, thumbnail, status, tags } = req.body;
    const newNews = new News({
        title,
        contentHtml,
        author,
        thumbnail,
        status,
        tags
    });
    await newNews.save();
    res.json({
        success: true,
        data: newNews
    })
}