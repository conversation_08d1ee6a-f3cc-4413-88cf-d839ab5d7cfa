.loginPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loginContainer {
  width: 100%;
  max-width: 400px;
}

.loginCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 40px;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loginHeader {
  text-align: center;
  margin-bottom: 32px;
}

.loginTitle {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.loginSubtitle {
  color: #6b7280;
  font-size: 16px;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.errorAlert {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formLabel {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.formInput {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background-color: white;
}

.formInput:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formInput:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
}

.inputError {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.passwordInput {
  position: relative;
}

.passwordToggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #6b7280;
  padding: 4px;
}

.passwordToggle:hover {
  color: #374151;
}

.passwordToggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.errorText {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.formOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.forgotLink {
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.forgotLink:hover {
  text-decoration: underline;
}

.loginButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.loginButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.loginButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loginFooter {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.loginFooter p {
  color: #6b7280;
  font-size: 14px;
}

.registerLink {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
}

.registerLink:hover {
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 480px) {
  .loginCard {
    padding: 24px;
  }
  
  .loginTitle {
    font-size: 24px;
  }
}
